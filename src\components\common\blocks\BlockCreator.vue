<template>
  <q-page>
    <draggable
      v-model="draggableBlocks"
      :component-data="{
        tag: 'div',
        type: 'transition-group',
        name: !isDragging ? 'flip-list' : null,
      }"
      item-key="id"
      handle=".three-dot-menu"
      :animation="200"
      ghost-class="ghost"
      chosen-class="chosen"
      drag-class="drag"
      @start="onDragStart"
      @end="onDragEnd"
      @change="onDragChange"
    >
      <template #item="{ element: block, index }">
        <div
          :key="block.id"
          :ref="(el) => (blockRefs[block.id] = el)"
          class="row justify-center draggable-item"
          :class="{ 'is-dragging': isDragging }"
        >
          <div class="col-auto">
            <div
              v-if="store.isSectionBlock(index) && store.totalSections > 1"
              class="col-12 section-container"
            >
              <div class="section-tab">
                ส่วนที่ {{ store.getSectionNumber(index) }} จาก {{ store.totalSections }}
              </div>
            </div>
            <div class="block-container">
              <div class="block-content full-width" @click="setSelectedBlock(block)">
                <template v-if="block.type === 'HEADER'">
                  <HeaderBlock
                    :itemBlock="block"
                    :index="index"
                    :type="props.type"
                    class="evaluate-item"
                    :class="{
                      'no-top-left-radius': store.isSectionBlock(index) && store.totalSections > 1,
                    }"
                    @focus-fab="store.selectedBlockId = `block-${block.id}`"
                    @duplicate="() => handleDuplicateHeaderBlock(block, index)"
                    @delete="() => onClickDeleteBlock(block, index)"
                  />
                </template>

                <template v-else-if="block.type === 'IMAGE'">
                  <ImageBlock
                    :item-block="block"
                    class="evaluate-item"
                    @focus-fab="allowFocusFab && (store.selectedBlockId = `block-${block.id}`)"
                    @duplicate="() => handleDuplicateBlock(block, index)"
                    @delete="() => onClickDeleteBlock(block, index)"
                  />
                </template>

                <template v-else>
                  <ItemBlockProvider :block-id="block.id" :item-block="block">
                    <ItemBlockComponent
                      :item-block="block"
                      :type="props.type"
                      class="evaluate-item"
                      @focus-fab="allowFocusFab && (store.selectedBlockId = `block-${block.id}`)"
                      @duplicate="() => handleDuplicateBlock(block, index)"
                      @delete="() => onClickDeleteBlock(block, index)"
                      @update:question="handleQuestionUpdate"
                      @update:option="handleOptionUpdate"
                      @update:is-required="handleIsRequiredUpdate"
                    />
                  </ItemBlockProvider>
                </template>
              </div>
            </div>
          </div>

          <div class="col-auto fixed-fab-col">
            <FloatActionBtnForBlock
              v-show="store.selectedBlockId === `block-${block.id}` && !isDragging"
              :disabled="isCreatingBlock"
              @add="() => handleAddBlockAfter(index)"
              @add-text="() => handleAddHeaderAfter(index)"
              @add-section="() => handleAddSection()"
              @add-image="(imageData) => handleAddImage(index, imageData)"
            />
          </div>
        </div>
      </template>
    </draggable>
  </q-page>
</template>

<script setup lang="ts">
// Define component name for keep-alive
defineOptions({
  name: 'block-creator',
});

import { ref, watch, nextTick, onMounted, computed, type ComponentPublicInstance } from 'vue';
import { useBlockCreatorStore } from 'src/stores/block_creator';
import type { ItemBlock, Option } from 'src/types/models';
import HeaderBlock from './HeaderBlock.vue';
import ItemBlockComponent from './ItemBlockComponent.vue';
import FloatActionBtnForBlock from './FloatActionBtnForBlock.vue';
import ItemBlockProvider from './ItemBlockProvider.vue';
import ImageBlock from './ImageBlock.vue';
import { useEvaluateFormStore } from 'src/stores/evaluate/form';
import { createItemBlockStore } from 'src/stores/item_block_store';
import { defaultBlocks } from 'src/data/defaultBlocks';
import draggable from 'vuedraggable';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { Notify } from 'quasar';

const props = defineProps<{
  blocks: ItemBlock[];
  type: 'quiz' | 'evaluate';
  assessmentId?: number | null;
}>();

// Use the new block creator store
const store = useBlockCreatorStore();
const evaluateFormStore = useEvaluateFormStore();

// Initialize assessment service based on type
const assessmentService = new AssessmentService(props.type);

// State management
const selectedBlock = ref<ItemBlock>();
const allowFocusFab = ref(true);
const isDragging = ref(false);
const isCreatingBlock = ref(false);

// Draggable blocks computed property
const draggableBlocks = computed({
  get: () => store.blocks,
  set: (newBlocks: ItemBlock[]) => {
    store.updateBlocksOrder(newBlocks);
  },
});

// Function to initialize blocks based on current data
const initializeBlocksFromData = async () => {
  let blocksToUse: ItemBlock[] = [];

  if (props.type === 'evaluate') {
    // For evaluate type, get blocks from the evaluate form store
    const assessmentBlocks = evaluateFormStore.currentAssessment?.itemBlocks;
    if (assessmentBlocks && assessmentBlocks.length > 0) {
      blocksToUse = assessmentBlocks;
    } else if (props.blocks && props.blocks.length > 0) {
      blocksToUse = props.blocks;
    } else {
      // Only use default blocks as last resort for evaluate type
      blocksToUse = defaultBlocks;
    }
  } else {
    // For quiz type, use props or default blocks
    blocksToUse = props.blocks && props.blocks.length > 0 ? props.blocks : defaultBlocks;
  }

  store.initializeBlocks(blocksToUse);

  // Initialize with first block selected
  if (store.blocks.length > 0) {
    store.selectedBlockId = `block-${store.blocks[0]!.id}`;
    await nextTick();
    scrollToTarget();
  }
};

onMounted(async () => {
  await initializeBlocksFromData();
});

// Watch for changes in the evaluate form store's current assessment
// This ensures that when data is fetched from backend, the blocks are re-initialized
watch(
  () => evaluateFormStore.currentAssessment,
  async (newAssessment) => {
    if (props.type === 'evaluate' && newAssessment?.itemBlocks) {
      await initializeBlocksFromData();
    }
  },
  { deep: true, immediate: false },
);

// Watch for changes in globalIsRequired to update all itemBlocks reactively
watch(
  () => evaluateFormStore.currentAssessment?.globalIsRequired,
  (newGlobalIsRequired, oldGlobalIsRequired) => {
    console.log('🔍 BlockCreator watcher triggered:', {
      type: props.type,
      newValue: newGlobalIsRequired,
      oldValue: oldGlobalIsRequired,
      hasAssessment: !!evaluateFormStore.currentAssessment,
      blocksCount: store.blocks.length,
    });

    if (props.type === 'evaluate' && newGlobalIsRequired !== undefined) {
      console.log('🔄 Global isRequired changed, updating all itemBlocks:', newGlobalIsRequired);

      // Update all non-header and non-image blocks in the local store using proper store methods
      let updatedCount = 0;
      store.blocks.forEach((block, index) => {
        if (block.type !== 'HEADER' && block.type !== 'IMAGE') {
          console.log(
            `📝 Updating block ${block.id} (index ${index}) isRequired: ${block.isRequired} → ${newGlobalIsRequired}`,
          );

          // Create updated block object
          const updatedBlock = {
            ...block,
            isRequired: newGlobalIsRequired,
          };

          // Use store's updateBlock method to ensure reactivity
          store.updateBlock(updatedBlock, index);
          updatedCount++;
        }
      });

      console.log(
        `✅ Updated ${updatedCount} blocks in local store to match global isRequired setting`,
      );
    }
  },
  { immediate: false },
);

// Utility functions
// Note: IDs are now generated by the backend, so we don't need nextId for new blocks

// Block refs for scrolling/focus
async function setFabAndScroll(id: number) {
  allowFocusFab.value = false;
  store.selectedBlockId = `block-${id}`;
  await nextTick();
  await nextTick();
  scrollToTarget();
  setTimeout(() => (allowFocusFab.value = true), 100);
}

function scrollToTarget() {
  if (!store.selectedBlockId) return;
  const id = Number(store.selectedBlockId.split('-')[1]);
  const el = blockRefs[id];
  if (el && 'scrollIntoView' in el) {
    (el as HTMLElement).scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
}

const setSelectedBlock = (item: ItemBlock) => {
  selectedBlock.value = item;
  store.selectedBlockId = `block-${item.id}`;
};

// Handle question updates from ItemBlockComponent
const handleQuestionUpdate = (updateData: {
  questionId?: number;
  questionText?: string;
  itemBlockId: number;
  updatedQuestion?: object;
  updatedBlock?: ItemBlock;
  typeChanged?: boolean;
}) => {
  // Handle type changes
  if (updateData.typeChanged && updateData.updatedBlock) {
    console.log('🔄 Handling ItemBlock type change:', {
      blockId: updateData.itemBlockId,
      newType: updateData.updatedBlock.type,
    });

    // Update the block type in the local store
    const blockIndex = store.blocks.findIndex((block) => block.id === updateData.itemBlockId);
    if (blockIndex !== -1) {
      // Replace the entire block with the updated one from backend
      store.blocks[blockIndex] = updateData.updatedBlock;
    }

    // Update the evaluate form store if this is an evaluate type
    if (props.type === 'evaluate' && evaluateFormStore.currentAssessment?.itemBlocks) {
      const assessmentBlockIndex = evaluateFormStore.currentAssessment.itemBlocks.findIndex(
        (block) => block.id === updateData.itemBlockId,
      );
      if (assessmentBlockIndex !== -1) {
        // Replace the entire block with the updated one from backend
        evaluateFormStore.currentAssessment.itemBlocks[assessmentBlockIndex] =
          updateData.updatedBlock;
      }
    }
    return;
  }

  // Handle question text updates (existing logic)
  if (updateData.questionId && updateData.questionText !== undefined) {
    // Update the question in the local store
    const blockIndex = store.blocks.findIndex((block) => block.id === updateData.itemBlockId);
    if (blockIndex !== -1) {
      const block = store.blocks[blockIndex];
      if (block?.questions && block.questions.length > 0) {
        const questionIndex = block.questions.findIndex((q) => q.id === updateData.questionId);
        if (questionIndex !== -1 && block.questions[questionIndex]) {
          // Update the question text in the store
          block.questions[questionIndex].questionText = updateData.questionText;
        }
      }
    }

    // Update the evaluate form store if this is an evaluate type
    if (props.type === 'evaluate' && evaluateFormStore.currentAssessment?.itemBlocks) {
      const assessmentBlockIndex = evaluateFormStore.currentAssessment.itemBlocks.findIndex(
        (block) => block.id === updateData.itemBlockId,
      );
      if (assessmentBlockIndex !== -1) {
        const assessmentBlock =
          evaluateFormStore.currentAssessment.itemBlocks[assessmentBlockIndex];
        if (assessmentBlock?.questions && assessmentBlock.questions.length > 0) {
          const questionIndex = assessmentBlock.questions.findIndex(
            (q) => q.id === updateData.questionId,
          );
          if (questionIndex !== -1 && assessmentBlock.questions[questionIndex]) {
            // Update the question text in the evaluate form store
            assessmentBlock.questions[questionIndex].questionText = updateData.questionText;
          }
        }
      }
    }
  }
};

// Handle option updates from ItemBlockComponent
const handleOptionUpdate = (updateData: {
  action: 'created' | 'updated';
  itemBlockId: number;
  option?: Option;
  optionId?: number;
  updateData?: { index: number; option: Option };
}) => {
  console.log('🔍 BlockCreator received option update:', updateData);

  // Find the block in the local store
  const blockIndex = store.blocks.findIndex((block) => block.id === updateData.itemBlockId);
  if (blockIndex === -1) {
    console.error('❌ Block not found for option update:', updateData.itemBlockId);
    return;
  }

  const block = store.blocks[blockIndex];
  if (!block) {
    console.error('❌ Block is undefined:', updateData.itemBlockId);
    return;
  }

  // Handle option creation
  if (updateData.action === 'created' && updateData.option) {
    console.log('✅ Adding new option to block:', updateData.option);

    // Initialize options array if it doesn't exist
    if (!block.options) {
      block.options = [];
    }

    // Add the new option to the block
    block.options.push(updateData.option);

    // Update the evaluate form store if this is an evaluate type
    if (props.type === 'evaluate' && evaluateFormStore.currentAssessment?.itemBlocks) {
      const assessmentBlockIndex = evaluateFormStore.currentAssessment.itemBlocks.findIndex(
        (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
      );
      if (assessmentBlockIndex !== -1) {
        const assessmentBlock =
          evaluateFormStore.currentAssessment.itemBlocks[assessmentBlockIndex];
        if (assessmentBlock) {
          // Initialize options array if it doesn't exist
          if (!assessmentBlock.options) {
            assessmentBlock.options = [];
          }
          // Add the new option to the assessment block
          assessmentBlock.options.push(updateData.option);
        }
      }
    }
  }

  // Handle option updates
  if (updateData.action === 'updated' && updateData.optionId && updateData.updateData) {
    console.log('✅ Updating existing option in block:', updateData.optionId);

    const { index, option } = updateData.updateData;

    // Update the option in the local store
    if (block.options && block.options[index]) {
      block.options[index] = option;
    }

    // Update the evaluate form store if this is an evaluate type
    if (props.type === 'evaluate' && evaluateFormStore.currentAssessment?.itemBlocks) {
      const assessmentBlockIndex = evaluateFormStore.currentAssessment.itemBlocks.findIndex(
        (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
      );
      if (assessmentBlockIndex !== -1) {
        const assessmentBlock =
          evaluateFormStore.currentAssessment.itemBlocks[assessmentBlockIndex];
        if (assessmentBlock?.options && assessmentBlock.options[index]) {
          // Update the option in the assessment block
          assessmentBlock.options[index] = option;
        }
      }
    }
  }
};

// Handle isRequired updates from ItemBlockComponent
const handleIsRequiredUpdate = (updateData: { itemBlockId: number; isRequired: boolean }) => {
  console.log('🔍 BlockCreator received isRequired update:', updateData);

  // Find the block in the local store
  const blockIndex = store.blocks.findIndex((block) => block.id === updateData.itemBlockId);
  if (blockIndex === -1) {
    console.error('❌ Block not found for isRequired update:', updateData.itemBlockId);
    return;
  }

  const block = store.blocks[blockIndex];
  if (!block) {
    console.error('❌ Block is undefined:', updateData.itemBlockId);
    return;
  }

  // Update the isRequired property in the local store using proper store method
  const updatedBlock = {
    ...block,
    isRequired: updateData.isRequired,
  };
  store.updateBlock(updatedBlock, blockIndex);

  // Update the evaluate form store if this is an evaluate type
  if (props.type === 'evaluate' && evaluateFormStore.currentAssessment?.itemBlocks) {
    const assessmentBlockIndex = evaluateFormStore.currentAssessment.itemBlocks.findIndex(
      (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
    );
    if (assessmentBlockIndex !== -1) {
      const assessmentBlock = evaluateFormStore.currentAssessment.itemBlocks[assessmentBlockIndex];
      if (assessmentBlock) {
        // Update the isRequired property in the assessment block
        assessmentBlock.isRequired = updateData.isRequired;
      }
    }
  }

  console.log(
    `✅ Updated isRequired for block ${updateData.itemBlockId} to: ${updateData.isRequired}`,
  );
};

// Helper function to determine the correct section for a new block
function getCurrentSection(index: number): number {
  // Find the section of the block at the given index
  const currentBlock = store.blocks[index];
  if (currentBlock) {
    return currentBlock.section;
  }

  // If no current block, find the section of the nearest previous block
  for (let i = index - 1; i >= 0; i--) {
    const block = store.blocks[i];
    if (block) {
      return block.section;
    }
  }

  // Default to section 1 if no blocks found
  return 1;
}

// Block operations
const handleAddBlockAfter = async (index: number) => {
  // Prevent multiple simultaneous block creation
  if (isCreatingBlock.value) {
    return;
  }

  try {
    isCreatingBlock.value = true;

    // Enhanced ID validation using store helpers
    const assessmentId = props.assessmentId || evaluateFormStore.getAssessmentId();

    if (!assessmentId) {
      Notify.create({
        message: 'ไม่พบ Assessment ID - กรุณาลองใหม่อีกครั้ง',
        type: 'negative',
        position: 'top',
      });
      return;
    }

    // Validate that we have proper ID structure
    const validation = evaluateFormStore.validateIds();
    if (!validation.valid) {
      console.warn('⚠️ ID validation failed before adding block:', validation.missing);
    }

    const globalIsRequired = evaluateFormStore.currentAssessment?.globalIsRequired ?? false;
    const currentSection = getCurrentSection(index);

    const newBlockData = {
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: 'RADIO' as const,
      isRequired: globalIsRequired,
    };

    // Show loading notification
    const loadingNotify = Notify.create({
      message: 'กำลังสร้างคำถามใหม่...',
      type: 'ongoing',
      position: 'top',
      timeout: 0,
      spinner: true,
    });

    // Call backend API to create the block
    const addedBlock = await assessmentService.createBlock(newBlockData);

    // Dismiss loading notification
    loadingNotify();

    if (addedBlock) {
      // Add to local store with backend response data
      store.addBlock(addedBlock, index);

      // Update the evaluate form store if this is an evaluate type
      if (props.type === 'evaluate' && evaluateFormStore.currentAssessment) {
        // Add the new block to the current assessment in the store
        const currentBlocks = evaluateFormStore.currentAssessment.itemBlocks || [];
        evaluateFormStore.currentAssessment.itemBlocks = [...currentBlocks, addedBlock];
      }

      // Show success notification
      Notify.create({
        message: 'เพิ่มคำถามใหม่เรียบร้อยแล้ว',
        type: 'positive',
        position: 'top',
      });

      await setFabAndScroll(addedBlock.id);
    } else {
      Notify.create({
        message: 'ไม่สามารถเพิ่มคำถามได้ - ไม่มีการตอบกลับจาก backend',
        type: 'negative',
        position: 'top',
      });
    }
  } catch {
    // Show error notification
    Notify.create({
      message: 'เกิดข้อผิดพลาดในการเพิ่มคำถาม - กรุณาลองใหม่อีกครั้ง',
      type: 'negative',
      position: 'top',
    });
  } finally {
    isCreatingBlock.value = false;
  }
};

const handleAddHeaderAfter = async (index: number) => {
  // Prevent multiple simultaneous block creation
  if (isCreatingBlock.value) {
    console.log('Header block creation already in progress, ignoring request');
    return;
  }

  try {
    isCreatingBlock.value = true;
    console.log('🚀 Starting header block creation process...');

    const assessmentId = props.assessmentId || evaluateFormStore.getAssessmentId();
    if (!assessmentId) {
      console.error('❌ Assessment ID is required for creating header block');
      Notify.create({
        message: 'ไม่พบ Assessment ID - กรุณาลองใหม่อีกครั้ง',
        type: 'negative',
        position: 'top',
      });
      return;
    }

    const currentSection = getCurrentSection(index);

    const newHeaderData = {
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: 'HEADER' as const,
      isRequired: false,
    };

    console.log('📝 Creating new header block with payload:', newHeaderData);

    // Show loading notification
    const loadingNotify = Notify.create({
      message: 'กำลังสร้างหัวข้อใหม่...',
      type: 'ongoing',
      position: 'top',
      timeout: 0,
      spinner: true,
    });

    // Call backend API to create the header block
    console.log('🌐 Calling backend API to create header block...');
    const addedBlock = await assessmentService.createBlock(newHeaderData);

    // Dismiss loading notification
    loadingNotify();

    if (addedBlock) {
      // Add to local store with backend response data
      store.addBlock(addedBlock, index);

      // Update the evaluate form store if this is an evaluate type
      if (props.type === 'evaluate' && evaluateFormStore.currentAssessment) {
        // Add the new block to the current assessment in the store
        const currentBlocks = evaluateFormStore.currentAssessment.itemBlocks || [];
        evaluateFormStore.currentAssessment.itemBlocks = [...currentBlocks, addedBlock];
      }

      // Show success notification
      Notify.create({
        message: 'เพิ่มหัวข้อใหม่เรียบร้อยแล้ว',
        type: 'positive',
        position: 'top',
      });

      await setFabAndScroll(addedBlock.id);
    } else {
      console.error('❌ Failed to create header block - no response from backend');
      Notify.create({
        message: 'ไม่สามารถเพิ่มหัวข้อได้ - ไม่มีการตอบกลับจาก backend',
        type: 'negative',
        position: 'top',
      });
    }
  } catch (error) {
    console.error('❌ Error creating header block:', error);
    console.error('Error context:', {
      assessmentId: props.assessmentId || evaluateFormStore.getAssessmentId(),
      currentAssessment: !!evaluateFormStore.currentAssessment,
      blockIndex: index,
    });

    // Show error notification
    Notify.create({
      message: 'เกิดข้อผิดพลาดในการเพิ่มหัวข้อ - กรุณาลองใหม่อีกครั้ง',
      type: 'negative',
      position: 'top',
    });
  } finally {
    isCreatingBlock.value = false;
    console.log('🏁 Header block creation process completed');
  }
};

const handleAddSection = async () => {
  // Prevent multiple simultaneous section creation
  if (isCreatingBlock.value) {
    console.log('Section creation already in progress, ignoring request');
    return;
  }

  try {
    isCreatingBlock.value = true;
    console.log('🚀 Starting section creation process...');

    const assessmentId = props.assessmentId || evaluateFormStore.getAssessmentId();
    if (!assessmentId) {
      console.error('❌ Assessment ID is required for creating section');
      Notify.create({
        message: 'ไม่พบ Assessment ID - กรุณาลองใหม่อีกครั้ง',
        type: 'negative',
        position: 'top',
      });
      return;
    }

    // Validate that we have proper ID structure
    const validation = evaluateFormStore.validateIds();
    if (!validation.valid) {
      console.warn('⚠️ ID validation failed before adding section:', validation.missing);
    }

    const globalIsRequired = evaluateFormStore.currentAssessment?.globalIsRequired ?? false;

    // Calculate the new section number
    const newSectionNumber = store.totalSections + 1;

    // Calculate the next sequence numbers based on the current blocks length
    const nextHeaderSequence = store.blocks.length + 1;
    const nextItemSequence = store.blocks.length + 2;

    // Create header block data
    const headerBlockData = {
      assessmentId: assessmentId,
      sequence: nextHeaderSequence,
      section: newSectionNumber,
      type: 'HEADER' as const,
      isRequired: false,
    };

    // Create item block data
    const itemBlockData = {
      assessmentId: assessmentId,
      sequence: nextItemSequence,
      section: newSectionNumber,
      type: 'RADIO' as const,
      isRequired: globalIsRequired,
    };

    console.log('📝 Creating new section with header and item blocks:', {
      headerBlockData,
      itemBlockData,
    });

    // Show loading notification
    const loadingNotify = Notify.create({
      message: 'กำลังสร้างส่วนใหม่...',
      type: 'ongoing',
      position: 'top',
      timeout: 0,
      spinner: true,
    });

    // Create header block first
    console.log('🌐 Creating header block...');
    const createdHeaderBlock = await assessmentService.createBlock(headerBlockData);
    if (!createdHeaderBlock) {
      console.error('❌ Failed to create header block for new section');
      loadingNotify();
      Notify.create({
        message: 'ไม่สามารถสร้างหัวข้อส่วนใหม่ได้',
        type: 'negative',
        position: 'top',
      });
      return;
    }

    // Create item block second
    console.log('🌐 Creating item block...');
    const createdItemBlock = await assessmentService.createBlock(itemBlockData);
    if (!createdItemBlock) {
      console.error('❌ Failed to create item block for new section');
      loadingNotify();
      Notify.create({
        message: 'ไม่สามารถสร้างคำถามส่วนใหม่ได้',
        type: 'negative',
        position: 'top',
      });
      return;
    }

    // Dismiss loading notification
    loadingNotify();

    console.log('✅ Section blocks successfully created:', {
      headerBlock: createdHeaderBlock,
      itemBlock: createdItemBlock,
    });

    // Append the blocks to the end of the blocks array (local store)
    store.appendBlock(createdHeaderBlock);
    store.appendBlock(createdItemBlock);

    // 🔧 FIX: Update the evaluate form store if this is an evaluate type
    if (props.type === 'evaluate' && evaluateFormStore.currentAssessment) {
      console.log('🔄 Synchronizing new section blocks with evaluate form store...');

      // Add the new blocks to the current assessment in the store
      const currentBlocks = evaluateFormStore.currentAssessment.itemBlocks || [];
      evaluateFormStore.currentAssessment.itemBlocks = [
        ...currentBlocks,
        createdHeaderBlock,
        createdItemBlock,
      ];

      console.log('✅ Evaluate form store updated with new section blocks');
    }

    // Show success notification
    Notify.create({
      message: 'เพิ่มส่วนใหม่เรียบร้อยแล้ว',
      type: 'positive',
      position: 'top',
    });

    // Focus on the new header block
    await setFabAndScroll(createdHeaderBlock.id);
  } catch (error) {
    console.error('❌ Error creating new section:', error);
    console.error('Error context:', {
      assessmentId: props.assessmentId || evaluateFormStore.getAssessmentId(),
      currentAssessment: !!evaluateFormStore.currentAssessment,
      totalSections: store.totalSections,
    });

    // Show error notification
    Notify.create({
      message: 'เกิดข้อผิดพลาดในการสร้างส่วนใหม่ - กรุณาลองใหม่อีกครั้ง',
      type: 'negative',
      position: 'top',
    });
  } finally {
    isCreatingBlock.value = false;
    console.log('🏁 Section creation process completed');
  }
};

const handleDuplicateHeaderBlock = async (source: ItemBlock, index: number) => {
  if (source.type !== 'HEADER') return;

  try {
    const assessmentId = props.assessmentId || evaluateFormStore.getAssessmentId();
    if (!assessmentId) {
      console.error('Assessment ID is required for duplicating header block');
      return;
    }

    const currentSection = getCurrentSection(index);

    const duplicateHeaderData = {
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: 'HEADER' as const,
      isRequired: source.isRequired,
    };

    console.log('Duplicating header block with payload:', duplicateHeaderData);

    // Call backend API to create the duplicated header block
    const duplicatedBlock = await assessmentService.createBlock(duplicateHeaderData);

    if (duplicatedBlock) {
      console.log('Header block successfully duplicated:', duplicatedBlock);

      // Add to local store with backend response data
      store.addBlock(duplicatedBlock, index);

      // Update the evaluate form store if this is an evaluate type
      if (props.type === 'evaluate' && evaluateFormStore.currentAssessment) {
        // Add the new block to the current assessment in the store
        const currentBlocks = evaluateFormStore.currentAssessment.itemBlocks || [];
        evaluateFormStore.currentAssessment.itemBlocks = [...currentBlocks, duplicatedBlock];
      }

      await setFabAndScroll(duplicatedBlock.id);
    } else {
      console.error('Failed to duplicate header block - no response from backend');
    }
  } catch (error) {
    console.error('Error duplicating header block:', error);
  }
};

const handleDuplicateBlock = async (source: ItemBlock, index: number) => {
  if (source.type === 'HEADER' || source.type === 'IMAGE') return;

  try {
    const assessmentId = props.assessmentId || evaluateFormStore.getAssessmentId();
    if (!assessmentId) {
      console.error('Assessment ID is required for duplicating block');
      return;
    }

    const currentSection = getCurrentSection(index);

    const duplicateBlockData = {
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: source.type,
      isRequired: source.isRequired,
    };

    console.log('Duplicating block with payload:', duplicateBlockData);

    // Call backend API to create the duplicated block
    const duplicatedBlock = await assessmentService.createBlock(duplicateBlockData);

    if (duplicatedBlock) {
      console.log('Block successfully duplicated:', duplicatedBlock);

      // Add to local store with backend response data
      store.addBlock(duplicatedBlock, index);

      // Update the evaluate form store if this is an evaluate type
      if (props.type === 'evaluate' && evaluateFormStore.currentAssessment) {
        // Add the new block to the current assessment in the store
        const currentBlocks = evaluateFormStore.currentAssessment.itemBlocks || [];
        evaluateFormStore.currentAssessment.itemBlocks = [...currentBlocks, duplicatedBlock];
      }

      // Get the source block's store to access its data
      const sourceStore = createItemBlockStore(source.id, source);

      // Get the new block's store
      const newStore = createItemBlockStore(duplicatedBlock.id, duplicatedBlock);

      // Copy data from the source store to the new store based on the question type
      if (source.type === 'RADIO') {
        // Copy radio choices
        const sourceStoreInstance = sourceStore();
        const newStoreInstance = newStore();
        newStoreInstance.radioOptions = sourceStoreInstance.radioOptions.map((choice) => ({
          placeholder: choice.placeholder,
          value: choice.value,
          optionText: choice.optionText,
          score: choice.score,
          sequence: choice.sequence,
        }));
      } else if (source.type === 'CHECKBOX') {
        // Copy checkbox choices
        const sourceStoreInstance = sourceStore();
        const newStoreInstance = newStore();
        newStoreInstance.checkboxOptions = sourceStoreInstance.checkboxOptions.map((choice) => ({
          placeholder: choice.placeholder,
          value: choice.value,
          optionText: choice.optionText,
          score: choice.score,
          sequence: choice.sequence,
        }));
      } else if (source.type === 'GRID') {
        // Copy grid row questions and column choices
        const sourceStoreInstance = sourceStore();
        const newStoreInstance = newStore();
        newStoreInstance.gridRowQuestions = sourceStoreInstance.gridRowQuestions.map(
          (question) => ({
            label: question.label,
            value: question.value,
            sequence: question.sequence,
          }),
        );
        newStoreInstance.gridColumnOptions = sourceStoreInstance.gridColumnOptions.map(
          (choice) => ({
            label: choice.label,
            value: choice.value,
            optionText: choice.optionText,
            score: choice.score,
            sequence: choice.sequence,
          }),
        );
      }

      await setFabAndScroll(duplicatedBlock.id);
    } else {
      console.error('Failed to duplicate block - no response from backend');
    }
  } catch (error) {
    console.error('Error duplicating block:', error);
  }
};

const onClickDeleteBlock = async (item: ItemBlock, index: number) => {
  console.log('🔥 DELETE FUNCTION CALLED!', { itemType: item.type, itemId: item.id });

  // Enhanced pre-deletion validation
  if (!item.id) {
    console.error('❌ Cannot delete block: Missing item ID');
    Notify.create({
      message: 'ไม่สามารถลบได้ - ไม่พบ ID ของรายการ',
      type: 'negative',
      position: 'top',
    });
    return;
  }

  if (!item.assessmentId) {
    console.error('❌ Cannot delete block: Missing assessmentId');
    Notify.create({
      message: 'ไม่สามารถลบได้ - ไม่พบ Assessment ID',
      type: 'negative',
      position: 'top',
    });
    return;
  }

  try {
    // Enhanced validation for header blocks
    if (item.type === 'HEADER' && !item.headerBody) {
      Notify.create({
        message: 'ไม่สามารถลบหัวข้อได้ - ข้อมูลไม่ครบถ้วน',
        type: 'negative',
        position: 'top',
      });
      return;
    }

    // Enhanced validation using evaluate form store
    if (props.type === 'evaluate') {
      const deletionValidation = evaluateFormStore.validateBlockDeletion(item.id);

      if (!deletionValidation.canDelete) {
        Notify.create({
          message: `ไม่สามารถลบได้: ${deletionValidation.issues.join(', ')}`,
          type: 'negative',
          position: 'top',
        });
        return;
      }
    }

    // Show loading notification
    const loadingNotify = Notify.create({
      message: item.type === 'HEADER' ? 'กำลังลบหัวข้อ...' : 'กำลังลบ...',
      type: 'ongoing',
      position: 'top',
      timeout: 0,
      spinner: true,
    });

    // Delete the ItemBlock (this will cascade delete related entities)
    const deletedBlock = await assessmentService.deleteBlock(item);

    // Dismiss loading notification
    loadingNotify();

    if (deletedBlock !== undefined) {
      // Show success notification
      const isHeaderBlock = item.type === 'HEADER';
      Notify.create({
        message: isHeaderBlock ? 'ลบหัวข้อเรียบร้อยแล้ว' : 'ลบคำถามเรียบร้อยแล้ว',
        type: 'positive',
        position: 'top',
      });

      // Perform UI cleanup
      await handleBlockDeletionCleanup(item, index);
    } else {
      Notify.create({
        message: 'ไม่สามารถลบได้ - ไม่มีการตอบกลับจาก backend',
        type: 'negative',
        position: 'top',
      });
    }
  } catch {
    // Show error notification
    Notify.create({
      message: 'เกิดข้อผิดพลาดในการลบ - กรุณาลองใหม่อีกครั้ง',
      type: 'negative',
      position: 'top',
    });
  }
};

// Separate function to handle UI cleanup after successful deletion
const handleBlockDeletionCleanup = async (item: ItemBlock, index: number) => {
  // Remove from local store
  store.deleteBlock(index);

  // Update the evaluate form store if this is an evaluate type
  if (props.type === 'evaluate' && evaluateFormStore.currentAssessment) {
    const beforeFilter = evaluateFormStore.currentAssessment.itemBlocks || [];

    // Remove the block from the current assessment in the store
    evaluateFormStore.currentAssessment.itemBlocks = beforeFilter.filter(
      (block) => block.id !== item.id,
    );
  }

  // Handle focus after deletion
  if (store.blocks.length > 0) {
    const nearestIndex = Math.min(index, store.blocks.length - 1);
    const nearestBlock = store.blocks[nearestIndex];
    if (nearestBlock) {
      store.selectedBlockId = `block-${nearestBlock.id}`;
      await setFabAndScroll(nearestBlock.id);
    }
  } else {
    store.selectedBlockId = undefined;
  }
};

const handleAddImage = async (index: number, imageData: string) => {
  try {
    const assessmentId = props.assessmentId || evaluateFormStore.getAssessmentId();
    if (!assessmentId) {
      return;
    }

    const currentSection = getCurrentSection(index);

    const newImageData = {
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: 'IMAGE' as const,
      isRequired: false,
      // Include image data in a format the backend expects
      imageData: imageData,
    };

    // Call backend API to create the image block
    const addedBlock = await assessmentService.createBlock(newImageData);

    if (addedBlock) {
      // Add to local store with backend response data
      store.addBlock(addedBlock, index);

      // Update the evaluate form store if this is an evaluate type
      if (props.type === 'evaluate' && evaluateFormStore.currentAssessment) {
        // Add the new block to the current assessment in the store
        const currentBlocks = evaluateFormStore.currentAssessment.itemBlocks || [];
        evaluateFormStore.currentAssessment.itemBlocks = [...currentBlocks, addedBlock];
      }

      await setFabAndScroll(addedBlock.id);
    }
  } catch {
    // Image creation failed silently
  }
};

// Drag and drop event handlers
const onDragStart = () => {
  isDragging.value = true;
  allowFocusFab.value = false;
};

const onDragEnd = () => {
  isDragging.value = false;
  setTimeout(() => {
    allowFocusFab.value = true;
  }, 100);
};

const onDragChange = () => {
  // Handle drag change events if needed
};

// Watch for selectedBlockId changes to scroll
watch(
  () => store.selectedBlockId,
  () => {
    scrollToTarget();
  },
);

// Block refs for template
const blockRefs: Record<number, Element | ComponentPublicInstance | null> = new Proxy(
  {},
  {
    get(_target, id: string) {
      return store.getBlockRef(Number(id));
    },
    set(_target, id: string, el) {
      store.setBlockRef(Number(id), el);
      return true;
    },
  },
);
</script>

<style scoped>
.fixed-fab-col {
  width: 48px;
}

.section-container {
  position: relative;
  z-index: 1;
  margin-bottom: 0;
}

.section-tab {
  background-color: #673ab7;
  color: white;
  font-weight: 500;
  padding: 6px 16px;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  font-size: 14px;
  width: fit-content;
  position: relative;
  left: 0;
}

.no-top-left-radius {
  border-top-left-radius: 0 !important;
}

/* Drag and Drop Styles */
.draggable-item {
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.draggable-item.is-dragging {
  opacity: 0.8;
}

.block-container {
  position: relative;
  width: 100%;
}

.block-content {
  flex: 1;
}

/* Drag states */
.ghost {
  opacity: 0.5;
  background: #f0f0f0;
  border: 2px dashed #ccc;
}

.chosen {
  opacity: 0.8;
}

.drag {
  opacity: 0.5;
  transform: rotate(5deg);
}

/* Flip animation for smooth transitions */
.flip-list-move,
.flip-list-enter-active,
.flip-list-leave-active {
  transition: all 0.3s ease;
}

.flip-list-enter-from,
.flip-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.flip-list-leave-active {
  position: absolute;
}
</style>
